import React from 'react';
import { Menu, MenuItem } from '@material-ui/core';
import { ContextMenuState } from '../hooks/useContextMenu';

interface ContextMenuComponentProps {
  contextMenu: ContextMenuState | null;
  onClose: () => void;
  onAddFolder: () => void;
  onAddRequest: () => void;
  onRename: () => void;
  onDeleteFolder?: () => void;
  onDeleteRequest?: () => void;
}

export const ContextMenuComponent: React.FC<ContextMenuComponentProps> = ({
  contextMenu,
  onClose,
  onAddFolder,
  onAddRequest,
  onRename,
  onDeleteFolder,
  onDeleteRequest,
}) => {
  return (
    <Menu
      keepMounted
      open={contextMenu !== null}
      onClose={onClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
    >
      {contextMenu?.itemType === 'collection' && (
        <>
          <MenuItem onClick={onAddFolder}>
            Add Folder
          </MenuItem>
          <MenuItem onClick={onAddRequest}>
            Add Request
          </MenuItem>
          <MenuItem onClick={onRename}>
            Rename Collection
          </MenuItem>
        </>
      )}

      {contextMenu?.itemType === 'folder' && (
        <>
          <MenuItem onClick={onAddFolder}>
            Add Folder
          </MenuItem>
          <MenuItem onClick={onAddRequest}>
            Add Request
          </MenuItem>
          <MenuItem onClick={onRename}>
            Rename Folder
          </MenuItem>
          {onDeleteFolder && (
            <MenuItem onClick={onDeleteFolder}>
              Delete Folder
            </MenuItem>
          )}
        </>
      )}

      {contextMenu?.itemType === 'request' && (
        <>
          <MenuItem onClick={onRename}>
            Rename Request
          </MenuItem>
          {onDeleteRequest && (
            <MenuItem onClick={onDeleteRequest}>
              Delete Request
            </MenuItem>
          )}
        </>
      )}
    </Menu>
  );
};
