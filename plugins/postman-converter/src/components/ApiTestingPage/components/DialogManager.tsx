import React from 'react';
import { ApiCollection } from '../../../types';
import { ImportDialog } from '../ImportDialog';
import { ExportDialog } from '../ExportDialog';
import { CreateFolderDialog } from '../CreateFolderDialog';
import { CreateRequestDialog } from '../CreateRequestDialog';
import { RenameDialog } from './RenameDialog';

interface DialogManagerProps {
  // Import Dialog
  importDialogOpen: boolean;
  onCloseImportDialog: () => void;
  onImportCollection: (collection: any) => void;
  onImportEnvironment: (environment: any) => void;

  // Export Dialog
  exportDialogOpen: boolean;
  onCloseExportDialog: () => void;
  collections: ApiCollection[];
  environments: any[];

  // Create Folder Dialog
  createFolderDialogOpen: boolean;
  onCloseCreateFolderDialog: () => void;
  onCreateFolder: (folderName: string, parentId: string | null, collectionId: string) => void;
  selectedCollectionForAction: string;
  selectedFolderForAction: string;

  // Create Request Dialog
  createRequestDialogOpen: boolean;
  onCloseCreateRequestDialog: () => void;
  onCreateRequest: (requestName: string, method: string, url: string, parentId: string | null, collectionId: string) => void;

  // Rename Dialog
  renameDialogOpen: boolean;
  onCloseRenameDialog: () => void;
  onRename: (newName: string) => void;
  renameDialogData: {
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null;
}

export const DialogManager: React.FC<DialogManagerProps> = ({
  importDialogOpen,
  onCloseImportDialog,
  onImportCollection,
  onImportEnvironment,
  exportDialogOpen,
  onCloseExportDialog,
  collections,
  environments,
  createFolderDialogOpen,
  onCloseCreateFolderDialog,
  onCreateFolder,
  selectedCollectionForAction,
  selectedFolderForAction,
  createRequestDialogOpen,
  onCloseCreateRequestDialog,
  onCreateRequest,
  renameDialogOpen,
  onCloseRenameDialog,
  onRename,
  renameDialogData,
}) => {
  return (
    <>
      {/* Import Dialog */}
      <ImportDialog
        open={importDialogOpen}
        onClose={onCloseImportDialog}
        onImportCollection={onImportCollection}
        onImportEnvironment={onImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={onCloseExportDialog}
        collections={collections}
        environments={environments}
      />

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={onCloseCreateFolderDialog}
        onCreateFolder={onCreateFolder}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={onCloseCreateRequestDialog}
        onCreateRequest={onCreateRequest}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Rename Dialog */}
      {renameDialogData && (
        <RenameDialog
          open={renameDialogOpen}
          onClose={onCloseRenameDialog}
          onRename={onRename}
          itemType={renameDialogData.itemType}
          currentName={renameDialogData.currentName}
        />
      )}
    </>
  );
};
