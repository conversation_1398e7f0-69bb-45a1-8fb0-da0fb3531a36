import React from 'react';
import {
  Grid,
  Typography,
  makeStyles,
  Box,
} from '@material-ui/core';
import { InfoCard } from '@backstage/core-components';
import TestingIcon from '@material-ui/icons/BugReport';

import { ApiCollection } from '../../../types';
import { CollectionsSidebar } from './CollectionsSidebar/CollectionsSidebar';
import { RequestBuilder } from './RequestBuilder';
import { ResponseViewer } from './ResponseViewer';
import { EnvironmentManager } from './EnvironmentManager';
import { ResizablePanels } from './ResizablePanels';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
  mainContent: {
    flex: 1,
    minHeight: 0, // Important for flex children to shrink
  },
  requestResponseContainer: {
    height: 'calc(100vh - 300px)', // Adjust based on header and other elements
    minHeight: '600px',
  },
}));

interface ApiTestingLayoutProps {
  // Collections props
  collections: ApiCollection[];
  collectionsLoading: boolean;
  collectionsError: string | null;
  expandedFolders: Set<string>;
  selectedItemId: string;
  unsavedCollections: Set<string>;
  isSaving: boolean;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
  onAddCollection: () => void;
  onSaveCollection: (collectionId: string) => void;
  onSaveAllCollections: () => void;

  // Environment props
  environments: any[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  environmentDialogOpen: boolean;
  onEnvironmentChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onOpenEnvironmentDialog: () => void;
  onCloseEnvironmentDialog: () => void;
  onOpenExportDialog: () => void;
  onEnvironmentUpdate: (environments: any[]) => void;
  onSelectedEnvironmentChange: (id: string) => void;

  // Request props
  currentRequest: any;
  currentResponse: any;
  isLoading: boolean;
  tabValue: number;
  responseTabValue: number;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testError: string | null;
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  onRequestChange: (request: any) => void;
  onMethodChange: (method: string) => void;
  onUrlChange: (url: string) => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSendRequest: () => void;
  onSavePreRequestScript: () => void;
  onSaveTests: () => void;
  onRunTests: () => void;
  onGenerateTests: () => void;
}

export const ApiTestingLayout: React.FC<ApiTestingLayoutProps> = ({
  // Collections
  collections,
  collectionsLoading,
  collectionsError,
  expandedFolders,
  selectedItemId,
  unsavedCollections,
  isSaving,
  onFolderToggle,
  onItemSelect,
  onContextMenu,
  onAddCollection,
  onSaveCollection,
  onSaveAllCollections,

  // Environment
  environments,
  currentEnvironment,
  selectedEnvironmentId,
  environmentDialogOpen,
  onEnvironmentChange,
  onOpenEnvironmentDialog,
  onCloseEnvironmentDialog,
  onOpenExportDialog,
  onEnvironmentUpdate,
  onSelectedEnvironmentChange,

  // Request
  currentRequest,
  currentResponse,
  isLoading,
  tabValue,
  responseTabValue,
  isGeneratingTests,
  isRunningTests,
  testError,
  isSavingPreRequestScript,
  preRequestScriptError,
  onRequestChange,
  onMethodChange,
  onUrlChange,
  onTabChange,
  onResponseTabChange,
  onSendRequest,
  onSavePreRequestScript,
  onSaveTests,
  onRunTests,
  onGenerateTests,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              unsavedCollections={unsavedCollections}
              isSaving={isSaving}
              onFolderToggle={onFolderToggle}
              onItemSelect={onItemSelect}
              onContextMenu={onContextMenu}
              onAddCollection={onAddCollection}
              onSaveCollection={onSaveCollection}
              onSaveAllCollections={onSaveAllCollections}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment Manager */}
            <EnvironmentManager
              environments={environments}
              currentEnvironment={currentEnvironment}
              selectedEnvironmentId={selectedEnvironmentId}
              environmentDialogOpen={environmentDialogOpen}
              onEnvironmentChange={onEnvironmentChange}
              onOpenEnvironmentDialog={onOpenEnvironmentDialog}
              onCloseEnvironmentDialog={onCloseEnvironmentDialog}
              onOpenExportDialog={onOpenExportDialog}
              onEnvironmentUpdate={onEnvironmentUpdate}
              onSelectedEnvironmentChange={onSelectedEnvironmentChange}
            />

            {/* Resizable Request and Response Panels */}
            <Box className={classes.requestResponseContainer}>
              <ResizablePanels
                defaultTopHeight={60}
                minTopHeight={25}
                minBottomHeight={20}
                allowCollapse
                topPanel={
                  <RequestBuilder
                    currentRequest={currentRequest}
                    currentResponse={currentResponse}
                    isLoading={isLoading}
                    tabValue={tabValue}
                    currentEnvironment={environments.find(env => env.id === currentEnvironment)}
                    onRequestChange={onRequestChange}
                    onMethodChange={onMethodChange}
                    onUrlChange={onUrlChange}
                    onTabChange={onTabChange}
                    onSendRequest={onSendRequest}
                    onSavePreRequestScript={onSavePreRequestScript}
                    onSaveTests={onSaveTests}
                    onRunTests={onRunTests}
                    onGenerateTests={onGenerateTests}
                    isSavingPreRequestScript={isSavingPreRequestScript}
                    preRequestScriptError={preRequestScriptError}
                    isGeneratingTests={isGeneratingTests}
                    isRunningTests={isRunningTests}
                    testError={testError}
                  />
                }
                bottomPanel={
                  <ResponseViewer
                    response={currentResponse}
                    responseTabValue={responseTabValue}
                    onResponseTabChange={onResponseTabChange}
                  />
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};
