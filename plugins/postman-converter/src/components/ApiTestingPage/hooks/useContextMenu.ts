import { useState, useCallback } from 'react';
import { ApiCollection, ApiFolder } from '../../../types';

export interface ContextMenuState {
  mouseX: number;
  mouseY: number;
  itemId: string;
  itemType: 'collection' | 'folder' | 'request';
}

interface UseContextMenuProps {
  collections: ApiCollection[];
  setSelectedCollectionForAction: (id: string) => void;
  setSelectedFolderForAction: (id: string) => void;
  setCreateFolderDialogOpen: (open: boolean) => void;
  setCreateRequestDialogOpen: (open: boolean) => void;
  setRenameDialogOpen: (open: boolean) => void;
  setRenameDialogData: (data: any) => void;
  handleDeleteFolder: (collectionId: string, folderId: string) => void;
  handleDeleteRequest: (collectionId: string, requestId: string) => void;
}

export const useContextMenu = ({
  collections,
  setSelectedCollectionForAction,
  setSelectedFolderForAction,
  setCreateFolderDialogOpen,
  setCreateRequestDialogOpen,
  setRenameDialogOpen,
  setRenameDialogData,
  handleDeleteFolder,
  handleDeleteRequest,
}: UseContextMenuProps) => {
  
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);

  const handleContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  const handleAddFolderFromContext = useCallback(() => {
    if (contextMenu) {
      if (contextMenu.itemType === 'collection') {
        setSelectedCollectionForAction(contextMenu.itemId);
        setSelectedFolderForAction('');
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection that contains this folder
        const collection = collections.find(c => {
          const findFolder = (folders: ApiFolder[]): boolean => {
            return folders.some(f =>
              f.id === contextMenu.itemId ||
              (f.folders.length > 0 && findFolder(f.folders))
            );
          };
          return findFolder(c.folders);
        });
        if (collection) {
          setSelectedCollectionForAction(collection.id);
          setSelectedFolderForAction(contextMenu.itemId);
        }
      }
      setCreateFolderDialogOpen(true);
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, setSelectedCollectionForAction, setSelectedFolderForAction, setCreateFolderDialogOpen]);

  const handleAddRequestFromContext = useCallback(() => {
    if (contextMenu) {
      if (contextMenu.itemType === 'collection') {
        setSelectedCollectionForAction(contextMenu.itemId);
        setSelectedFolderForAction('');
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection that contains this folder
        const collection = collections.find(c => {
          const findFolder = (folders: ApiFolder[]): boolean => {
            return folders.some(f =>
              f.id === contextMenu.itemId ||
              (f.folders.length > 0 && findFolder(f.folders))
            );
          };
          return findFolder(c.folders);
        });
        if (collection) {
          setSelectedCollectionForAction(collection.id);
          setSelectedFolderForAction(contextMenu.itemId);
        }
      }
      setCreateRequestDialogOpen(true);
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, setSelectedCollectionForAction, setSelectedFolderForAction, setCreateRequestDialogOpen]);

  const handleRenameFromContext = useCallback(() => {
    if (contextMenu) {
      let currentName = '';
      let collectionId = '';

      if (contextMenu.itemType === 'collection') {
        const collection = collections.find(c => c.id === contextMenu.itemId);
        if (collection) {
          currentName = collection.name;
          collectionId = collection.id;
        }
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection and folder
        for (const collection of collections) {
          const findFolder = (folders: ApiFolder[]): ApiFolder | null => {
            for (const folder of folders) {
              if (folder.id === contextMenu.itemId) {
                return folder;
              }
              if (folder.folders.length > 0) {
                const found = findFolder(folder.folders);
                if (found) return found;
              }
            }
            return null;
          };
          const folder = findFolder(collection.folders);
          if (folder) {
            currentName = folder.name;
            collectionId = collection.id;
            break;
          }
        }
      } else if (contextMenu.itemType === 'request') {
        // Find the collection and request
        const collection = collections.find(c => c.requests[contextMenu.itemId]);
        if (collection && collection.requests[contextMenu.itemId]) {
          currentName = collection.requests[contextMenu.itemId].name;
          collectionId = collection.id;
        }
      }

      if (currentName && collectionId) {
        setRenameDialogData({
          itemType: contextMenu.itemType,
          itemId: contextMenu.itemId,
          collectionId,
          currentName,
        });
        setRenameDialogOpen(true);
        handleContextMenuClose();
      }
    }
  }, [contextMenu, collections, handleContextMenuClose, setRenameDialogData, setRenameDialogOpen]);

  const handleDeleteFolderFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'folder') {
      // Find the collection that contains this folder
      const collection = collections.find(c => {
        const findFolder = (folders: ApiFolder[]): boolean => {
          return folders.some(f =>
            f.id === contextMenu.itemId ||
            (f.folders.length > 0 && findFolder(f.folders))
          );
        };
        return findFolder(c.folders);
      });

      if (collection) {
        handleDeleteFolder(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteFolder]);

  const handleDeleteRequestFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'request') {
      // Find the collection that contains this request
      const collection = collections.find(c => c.requests[contextMenu.itemId]);
      if (collection) {
        handleDeleteRequest(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteRequest]);

  return {
    contextMenu,
    handleContextMenu,
    handleContextMenuClose,
    handleAddFolderFromContext,
    handleAddRequestFromContext,
    handleRenameFromContext,
    handleDeleteFolderFromContext,
    handleDeleteRequestFromContext,
  };
};
