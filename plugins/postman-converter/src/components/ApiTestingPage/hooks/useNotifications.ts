import { useState, useCallback } from 'react';

export interface NotificationState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

export const useNotifications = () => {
  const [snackbar, setSnackbar] = useState<NotificationState>({
    open: false,
    message: '',
    severity: 'info',
  });

  const showNotification = useCallback((
    message: string,
    severity: NotificationState['severity'] = 'info'
  ) => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  }, []);

  const hideNotification = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  return {
    snackbar,
    showNotification,
    hideNotification,
  };
};
