import { useCallback } from 'react';
import { ApiCollection, ApiFolder } from '../../../types';
import { createNewRequest } from '../utils/requestUtils';

interface UseDeleteOperationsProps {
  collections: ApiCollection[];
  setCollections: React.Dispatch<React.SetStateAction<ApiCollection[]>>;
  selectedItemId: string;
  handleItemSelect: (itemId: string) => void;
  setCurrentRequest: (request: any) => void;
}

export const useDeleteOperations = ({
  collections,
  setCollections,
  selectedItemId,
  handleItemSelect,
  setCurrentRequest,
}: UseDeleteOperationsProps) => {
  
  const handleDeleteFolder = useCallback(async (collectionId: string, folderId: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Remove the folder from the collection structure
      const removeFolderRecursively = (folders: ApiFolder[]): ApiFolder[] => {
        return folders
          .filter(folder => folder.id !== folderId)
          .map(folder => ({
            ...folder,
            folders: removeFolderRecursively(folder.folders),
          }));
      };

      const updatedCollection = {
        ...collection,
        folders: removeFolderRecursively(collection.folders),
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Clear selected item if it was in this folder
      if (selectedItemId === folderId) {
        handleItemSelect('');
      }

      // TODO: Update the collection in the database
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  }, [collections, selectedItemId, setCollections, handleItemSelect]);

  const handleDeleteRequest = useCallback(async (collectionId: string, requestId: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Remove the request from the requests object
      const updatedRequests = { ...collection.requests };
      delete updatedRequests[requestId];

      // Remove the request from any folders that contain it
      const removeRequestFromFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => ({
          ...folder,
          requests: folder.requests.filter(reqId => reqId !== requestId),
          folders: removeRequestFromFolders(folder.folders),
        }));
      };

      // Remove from orphaned requests if it's there
      const updatedOrphanedRequests = (collection._orphanedRequests || [])
        .filter(reqId => reqId !== requestId);

      const updatedCollection = {
        ...collection,
        requests: updatedRequests,
        folders: removeRequestFromFolders(collection.folders),
        _orphanedRequests: updatedOrphanedRequests,
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Clear selected item if it was this request
      if (selectedItemId === requestId) {
        handleItemSelect('');
        setCurrentRequest(createNewRequest());
      }

      // TODO: Update the collection in the database
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  }, [collections, selectedItemId, setCollections, setCurrentRequest, handleItemSelect]);

  return {
    handleDeleteFolder,
    handleDeleteRequest,
  };
};
