import React, { useState, useCallback } from 'react';

// Utils
import { createNewRequest } from './utils/requestUtils';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';
import { useNotifications } from './hooks/useNotifications';
import { useDeleteOperations } from './hooks/useDeleteOperations';
import { useContextMenu } from './hooks/useContextMenu';

// Components
import { ApiTestingLayout } from './components/ApiTestingLayout';
import { DialogManager } from './components/DialogManager';
import { ContextMenuComponent } from './components/ContextMenuComponent';
import { NotificationSnackbar } from './components/NotificationSnackbar';



export const ApiTestingPage = () => {
  // Collections management
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    unsavedCollections,
    isSaving,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
  } = useCollections();

  // Handle save all collections
  const handleSaveAllCollections = useCallback(async () => {
    const savePromises = Array.from(unsavedCollections).map(collectionId =>
      handleSaveCollection(collectionId)
    );
    await Promise.all(savePromises);
  }, [unsavedCollections, handleSaveCollection]);

  // Request management
  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections);

  // Environment management
  const {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    handleImportEnvironment,
  } = useEnvironments();

  // Notifications
  const { snackbar, showNotification, hideNotification } = useNotifications();

  // Dialog states
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<{
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>(environments[0]?.id || '');

  // Delete operations hook
  const { handleDeleteFolder, handleDeleteRequest } = useDeleteOperations({
    collections,
    setCollections,
    selectedItemId,
    handleItemSelect,
    setCurrentRequest,
  });

  // Context menu hook
  const {
    contextMenu,
    handleContextMenu,
    handleContextMenuClose,
    handleAddFolderFromContext,
    handleAddRequestFromContext,
    handleRenameFromContext,
    handleDeleteFolderFromContext,
    handleDeleteRequestFromContext,
  } = useContextMenu({
    collections,
    setSelectedCollectionForAction,
    setSelectedFolderForAction,
    setCreateFolderDialogOpen,
    setCreateRequestDialogOpen,
    setRenameDialogOpen,
    setRenameDialogData,
    handleDeleteFolder,
    handleDeleteRequest,
  });

  // Handle environment change
  const handleEnvironmentChange = useCallback((event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentEnvironment(event.target.value as string);
  }, [setCurrentEnvironment]);

  // Handle send request with environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    return handleSendRequest(currentEnv);
  }, [handleSendRequest, environments, currentEnvironment]);

  // Load selected request when item is selected
  React.useEffect(() => {
    if (selectedItemId) {
      const collection = collections.find(col => {
        return Object.keys(col.requests).includes(selectedItemId);
      });

      if (collection && collection.requests[selectedItemId]) {
        setCurrentRequest(collection.requests[selectedItemId]);
      }
    }
  }, [selectedItemId, collections, setCurrentRequest]);

  return (
    <>
      <ApiTestingLayout
        // Collections props
        collections={collections}
        collectionsLoading={collectionsLoading}
        collectionsError={collectionsError}
        expandedFolders={expandedFolders}
        selectedItemId={selectedItemId}
        unsavedCollections={unsavedCollections}
        isSaving={isSaving}
        onFolderToggle={handleFolderToggle}
        onItemSelect={handleItemSelect}
        onContextMenu={handleContextMenu}
        onAddCollection={handleAddCollection}
        onSaveCollection={handleSaveCollection}
        onSaveAllCollections={handleSaveAllCollections}

        // Environment props
        environments={environments}
        currentEnvironment={currentEnvironment}
        selectedEnvironmentId={selectedEnvironmentId}
        environmentDialogOpen={isEnvironmentDialogOpen}
        onEnvironmentChange={handleEnvironmentChange}
        onOpenEnvironmentDialog={() => setIsEnvironmentDialogOpen(true)}
        onCloseEnvironmentDialog={() => setIsEnvironmentDialogOpen(false)}
        onOpenExportDialog={() => setExportDialogOpen(true)}
        onEnvironmentUpdate={setEnvironments}
        onSelectedEnvironmentChange={setSelectedEnvironmentId}

        // Request props
        currentRequest={currentRequest}
        currentResponse={currentResponse}
        isLoading={isLoading}
        tabValue={tabValue}
        responseTabValue={responseTabValue}
        isGeneratingTests={isGeneratingTests}
        isRunningTests={isRunningTests}
        testError={testError}
        isSavingPreRequestScript={isSavingPreRequestScript}
        preRequestScriptError={preRequestScriptError}
        onRequestChange={setCurrentRequest}
        onMethodChange={handleMethodChange}
        onUrlChange={handleUrlChange}
        onTabChange={handleTabChange}
        onResponseTabChange={handleResponseTabChange}
        onSendRequest={handleSendRequestWithEnvironment}
        onSavePreRequestScript={handleSavePreRequestScript}
        onSaveTests={handleSaveTests}
        onRunTests={handleRunTests}
        onGenerateTests={handleGenerateTests}
      />

      <ContextMenuComponent
        contextMenu={contextMenu}
        onClose={handleContextMenuClose}
        onAddFolder={handleAddFolderFromContext}
        onAddRequest={handleAddRequestFromContext}
        onRename={handleRenameFromContext}
        onDeleteFolder={handleDeleteFolderFromContext}
        onDeleteRequest={handleDeleteRequestFromContext}
      />

      <DialogManager
        // Import Dialog
        importDialogOpen={importDialogOpen}
        onCloseImportDialog={() => setImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}

        // Export Dialog
        exportDialogOpen={exportDialogOpen}
        onCloseExportDialog={() => setExportDialogOpen(false)}
        collections={collections}
        environments={environments}

        // Create Folder Dialog
        createFolderDialogOpen={createFolderDialogOpen}
        onCloseCreateFolderDialog={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={(folderName, parentId, collectionId) => {
          handleAddFolder(collectionId, parentId || undefined, folderName);
        }}
        selectedCollectionForAction={selectedCollectionForAction}
        selectedFolderForAction={selectedFolderForAction}

        // Create Request Dialog
        createRequestDialogOpen={createRequestDialogOpen}
        onCloseCreateRequestDialog={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={(requestName, method, url, parentId, collectionId) => {
          handleAddRequest(collectionId, parentId || undefined, requestName, method, url);
        }}

        // Rename Dialog
        renameDialogOpen={renameDialogOpen}
        onCloseRenameDialog={() => {
          setRenameDialogOpen(false);
          setRenameDialogData(null);
        }}
        onRename={(newName) => {
          if (renameDialogData) {
            if (renameDialogData.itemType === 'collection') {
              handleRenameCollection(renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'folder') {
              handleRenameFolder(renameDialogData.collectionId, renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'request') {
              handleRenameRequest(renameDialogData.collectionId, renameDialogData.itemId, newName);
            }
          }
        }}
        renameDialogData={renameDialogData}
      />

      <NotificationSnackbar
        snackbar={snackbar}
        onClose={hideNotification}
      />
    </>
  );

  // Handle delete folder from context
  const handleDeleteFolderFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'folder') {
      // Find the collection that contains this folder
      const collection = collections.find(c => {
        const findFolder = (folders: ApiFolder[]): boolean => {
          return folders.some(f =>
            f.id === contextMenu.itemId ||
            (f.folders.length > 0 && findFolder(f.folders))
          );
        };
        return findFolder(c.folders);
      });

      if (collection) {
        handleDeleteFolder(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteFolder]);

  // Handle delete request from context
  const handleDeleteRequestFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'request') {
      // Find the collection that contains this request
      const collection = collections.find(c => c.requests[contextMenu.itemId]);
      if (collection) {
        handleDeleteRequest(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteRequest]);

  // Handle send request with environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    return handleSendRequest(currentEnv);
  }, [handleSendRequest, environments, currentEnvironment]);

  // Load selected request when item is selected
  React.useEffect(() => {
    if (selectedItemId) {
      const collection = collections.find(col => {
        return Object.keys(col.requests).includes(selectedItemId);
      });

      if (collection && collection.requests[selectedItemId]) {
        setCurrentRequest(collection.requests[selectedItemId]);
      }
    }
  }, [selectedItemId, collections, setCurrentRequest]);

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError ?? null}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              unsavedCollections={unsavedCollections}
              isSaving={isSaving}
              onFolderToggle={handleFolderToggle}
              onItemSelect={handleItemSelect}
              onContextMenu={handleContextMenu}
              onAddCollection={handleAddCollection}
              onSaveCollection={handleSaveCollection}
              onSaveAllCollections={handleSaveAllCollections}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment Manager */}
            <EnvironmentManager
              environments={environments}
              currentEnvironment={currentEnvironment}
              selectedEnvironmentId={selectedEnvironmentId}
              environmentDialogOpen={isEnvironmentDialogOpen}
              onEnvironmentChange={handleEnvironmentChange}
              onOpenEnvironmentDialog={() => setIsEnvironmentDialogOpen(true)}
              onCloseEnvironmentDialog={() => setIsEnvironmentDialogOpen(false)}
              onOpenExportDialog={() => setExportDialogOpen(true)}
              onEnvironmentUpdate={setEnvironments}
              onSelectedEnvironmentChange={setSelectedEnvironmentId}
            />

            {/* Resizable Request and Response Panels */}
            <Box className={classes.requestResponseContainer}>
              <ResizablePanels
                defaultTopHeight={60}
                minTopHeight={25}
                minBottomHeight={20}
                allowCollapse
                topPanel={
                  <RequestBuilder
                    currentRequest={currentRequest}
                    currentResponse={currentResponse}
                    isLoading={isLoading}
                    tabValue={tabValue}
                    currentEnvironment={environments.find(env => env.id === currentEnvironment)}
                    onRequestChange={setCurrentRequest}
                    onMethodChange={handleMethodChange}
                    onUrlChange={handleUrlChange}
                    onTabChange={handleTabChange}
                    onSendRequest={handleSendRequestWithEnvironment}
                    onSavePreRequestScript={handleSavePreRequestScript}
                    onSaveTests={handleSaveTests}
                    onRunTests={handleRunTests}
                    onGenerateTests={handleGenerateTests}
                    isSavingPreRequestScript={isSavingPreRequestScript}
                    preRequestScriptError={preRequestScriptError}
                    isGeneratingTests={isGeneratingTests}
                    isRunningTests={isRunningTests}
                    testError={testError}
                  />
                }
                bottomPanel={
                  <ResponseViewer
                    response={currentResponse}
                    responseTabValue={responseTabValue}
                    onResponseTabChange={handleResponseTabChange}
                  />
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import Dialog */}
      <ImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        collections={collections}
        environments={environments}
      />

      {/* Context Menu */}
      <Menu
        keepMounted
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextMenu?.itemType === 'collection' && (
          <>
            <MenuItem onClick={handleAddFolderFromContext}>
              Add Folder
            </MenuItem>
            <MenuItem onClick={handleAddRequestFromContext}>
              Add Request
            </MenuItem>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Collection
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'folder' && (
          <>
            <MenuItem onClick={handleAddFolderFromContext}>
              Add Folder
            </MenuItem>
            <MenuItem onClick={handleAddRequestFromContext}>
              Add Request
            </MenuItem>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Folder
            </MenuItem>
            <MenuItem onClick={handleDeleteFolderFromContext}>
              Delete Folder
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'request' && (
          <>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Request
            </MenuItem>
            <MenuItem onClick={handleDeleteRequestFromContext}>
              Delete Request
            </MenuItem>
          </>
        )}
      </Menu>

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={(folderName, parentId, collectionId) => {
          handleAddFolder(collectionId, parentId || undefined, folderName);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={(requestName, method, url, parentId, collectionId) => {
          handleAddRequest(collectionId, parentId || undefined, requestName, method, url);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Rename Dialog */}
      {renameDialogData && (
        <RenameDialog
          open={renameDialogOpen}
          onClose={() => {
            setRenameDialogOpen(false);
            setRenameDialogData(null);
          }}
          onRename={(newName) => {
            if (renameDialogData) {
              if (renameDialogData.itemType === 'collection') {
                handleRenameCollection(renameDialogData.itemId, newName);
              } else if (renameDialogData.itemType === 'folder') {
                handleRenameFolder(renameDialogData.collectionId, renameDialogData.itemId, newName);
              } else if (renameDialogData.itemType === 'request') {
                handleRenameRequest(renameDialogData.collectionId, renameDialogData.itemId, newName);
              }
            }
          }}
          itemType={renameDialogData.itemType}
          currentName={renameDialogData.currentName}
        />
      )}
    </div>
  );
};